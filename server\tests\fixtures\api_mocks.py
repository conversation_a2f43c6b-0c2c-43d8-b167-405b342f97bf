from typing import Dict, Any, List
from decimal import Decimal
from unittest.mock import Mo<PERSON>
from fastapi import Request, Response
import pytest

from src.core.schemas.general.component_schemas import (
    ComponentAdvancedSearchResponseSchema,
    ComponentReadSchema,
    ComponentSearchResultSchema
)

@pytest.fixture
def mock_app():
    """Create a mock ASGI app for testing."""
    return Mock()


@pytest.fixture
def mock_request():
    """Create a mock request object with proper attributes."""
    request = Mock(spec=Request)
    request.method = "GET"
    request.url = Mock()
    request.url.path = "/api/v1/test"
    request.url.query = ""
    request.query_params = {}
    request.headers = {}
    request.client.host = "127.0.0.1"
    return request


@pytest.fixture
def mock_response():
    """Create a mock response object."""
    response = Mock(spec=Response)
    response.status_code = 200
    response.headers = {}
    response.body = b'{"result": "success"}'
    return response


@pytest.fixture
def mock_call_next():
    """Create a mock call_next function that returns a response."""

    async def call_next(request):
        # Directly create a mock response within the fixture
        response = Mock(spec=Response)
        response.status_code = 200
        response.headers = {}
        # Add a default body that can be overridden in tests if needed
        response.body = b'{"message": "test"}'
        return response

    return call_next

@pytest.fixture
def mock_advanced_search_request() -> Dict[str, Any]:
    """Sample advanced search request data."""
    return {
        "search_term": "circuit breaker",
        "fuzzy_search": False,
        "basic_filters": [
            {
                "field": "manufacturer",
                "operator": "eq",
                "value": "Schneider Electric",
                "logical_operator": "and"
            }
        ],
        "specification_filters": [
            {
                "path": "electrical.voltage_rating",
                "operator": "gte",
                "value": 120,
                "data_type": "number",
                "logical_operator": "and"
            }
        ],
        "range_filters": [
            {
                "field": "unit_price",
                "min_value": 10.0,
                "max_value": 100.0,
                "include_min": True,
                "include_max": True
            }
        ],
        "sort_by": "name",
        "sort_order": "asc",
        "include_inactive": False
    }

@pytest.fixture
def mock_search_response() -> ComponentAdvancedSearchResponseSchema:
    """Sample search response data."""
    component_schema = ComponentReadSchema(
        id=1,
        manufacturer="Schneider Electric",
        model_number="QO120",
        name="Circuit Breaker 20A",
        description="Single pole circuit breaker",
        category="PROTECTION",
        component_type="CIRCUIT_BREAKER",
        unit_price=Decimal("25.99"),
        specifications={
            "electrical": {
                "voltage_rating": 120,
                "current_rating": 20
            }
        },
        is_active=True,
        is_deleted=False,
        created_at="2024-01-01T00:00:00Z",
        updated_at="2024-01-01T00:00:00Z"
    )
    
    search_result = ComponentSearchResultSchema(
        component=component_schema,
        relevance_score=0.95,
        matched_fields=["name", "manufacturer"]
    )
    
    return ComponentAdvancedSearchResponseSchema(
        items=[search_result],
        pagination={
            "page": 1,
            "per_page": 20,
            "total": 1,
            "pages": 1
        },
        search_metadata={
            "query_time": "< 1ms",
            "total_filters_applied": 3,
            "search_type": "advanced_builder",
            "fuzzy_search_enabled": False
        },
        suggestions=[]
    )

@pytest.fixture
def mock_bulk_create_request() -> List[Dict[str, Any]]:
    """Sample bulk create request data."""
    return [
        {
            "manufacturer": "Schneider Electric",
            "model_number": "QO120",
            "name": "Circuit Breaker 20A",
            "description": "Single pole circuit breaker",
            "category": "PROTECTION",
            "component_type": "CIRCUIT_BREAKER",
            "unit_price": 25.99,
            "specifications": {
                "electrical": {
                    "voltage_rating": 120,
                    "current_rating": 20
                }
            }
        },
        {
            "manufacturer": "ABB",
            "model_number": "S201-B16",
            "name": "MCB 16A Type B",
            "description": "Miniature circuit breaker",
            "category": "PROTECTION",
            "component_type": "CIRCUIT_BREAKER",
            "unit_price": 18.50,
            "specifications": {
                "electrical": {
                    "voltage_rating": 230,
                    "current_rating": 16
                }
            }
        }
    ]
