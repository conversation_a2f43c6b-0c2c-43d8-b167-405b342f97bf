#!/usr/bin/env python3
"""Component API Routes Tests for Ultimate Electrical Designer.

This module provides comprehensive tests for component management API endpoints,
including CRUD operations, search functionality, filtering, and error handling.

Key Test Areas:
- Component CRUD operations with validation
- Advanced search and filtering with pagination
- Component categorization and type filtering
- Authentication and authorization testing
- Error handling and edge cases
- Performance and load testing scenarios
- Advanced search endpoints with complex filtering
- Enhanced bulk operations endpoints
- Performance optimization endpoints
- Cache management endpoints
- Error handling and validation
"""

import json
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, patch

from fastapi import status
from fastapi.testclient import TestClient

from src.core.enums.electrical_enums import ComponentCategoryType, ComponentType
from src.core.errors.exceptions import BusinessLogicError, NotFoundError, ValidationError
from src.core.schemas.general.component_schemas import (
    ComponentCreateSchema,
    ComponentReadSchema,
    ComponentSearchSchema,
    ComponentUpdateSchema,
)
from fixtures.api_mocks import (
    mock_advanced_search_request,
    mock_bulk_create_request,
    mock_bulk_update_request,
    mock_search_response,
    mock_component_service,
    mock_auth_user
)

class TestComponentCRUDEndpoints:
    """Test suite for component CRUD operations."""

    def test_create_component_success(
        self,
        authenticated_client: TestClient,
        sample_component_data: Dict[str, Any]
    ):
        """Test successful component creation."""
        # Make real API call without mocking to test actual functionality
        response = authenticated_client.post("/api/v1/components/", json=sample_component_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data["name"] == sample_component_data["name"]
        assert response_data["manufacturer"] == sample_component_data["manufacturer"]
        assert response_data["model_number"] == sample_component_data["model_number"]
        assert "id" in response_data

    def test_create_component_validation_error(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test component creation with validation error."""
        invalid_data = {
            "name": "",  # Invalid: empty name
            "manufacturer": "ABB",
            "model_number": "S203-C16",
            "component_type": "INVALID_TYPE"  # Invalid enum value
        }

        mock_component_service.create_component.side_effect = ValidationError("Invalid component data")

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service):
            
            response = authenticated_client.post("/api/v1/components/", json=invalid_data)
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert "validation failed" in response.json()["detail"].lower()

    def test_create_component_duplicate_error(
        self,
        authenticated_client: TestClient,
        sample_component_data: Dict[str, Any],
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test component creation with duplicate error."""
        mock_component_service.create_component.side_effect = BusinessLogicError("Component already exists")

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service):
            
            response = authenticated_client.post("/api/v1/components/", json=sample_component_data)
            
            assert response.status_code == status.HTTP_409_CONFLICT
            assert "already exists" in response.json()["detail"]

    def test_get_component_success(
        self,
        authenticated_client: TestClient,
        sample_component_data: Dict[str, Any],
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test successful component retrieval."""
        # Fix the schema data to match ComponentReadSchema expectations
        component_data = sample_component_data.copy()
        # Convert dimensions and metadata to the _json fields expected by ComponentReadSchema
        component_data["dimensions_json"] = component_data.pop("dimensions")
        component_data["metadata_json"] = component_data.pop("metadata")
        
        component = ComponentReadSchema(
            id=1,
            **component_data,
            created_at=datetime.fromisoformat("2024-01-01T00:00:00+00:00"),
            updated_at=datetime.fromisoformat("2024-01-01T00:00:00+00:00")
        )
        mock_component_service.get_component.return_value = component

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service):
            
            response = authenticated_client.get("/api/v1/components/1")
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["id"] == 1
            assert response_data["name"] == sample_component_data["name"]
            mock_component_service.get_component.assert_called_once_with(1)

    def test_get_component_not_found(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test component retrieval with not found error."""
        mock_component_service.get_component.side_effect = NotFoundError(
            code="COMPONENT_NOT_FOUND",
            detail="Component with ID 999 was not found"
        )

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service):
            
            response = authenticated_client.get("/api/v1/components/999")
            
            assert response.status_code == status.HTTP_404_NOT_FOUND
            assert "not found" in response.json()["detail"].lower()

    def test_update_component_success(
        self,
        authenticated_client: TestClient,
        sample_component_data: Dict[str, Any],
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test successful component update."""
        update_data = {
            "name": "Updated Circuit Breaker",
            "unit_price": "30.00"
        }
        
        # Fix the schema data to match ComponentReadSchema expectations
        component_data = {**sample_component_data, **update_data}
        component_data["dimensions_json"] = component_data.pop("dimensions")
        component_data["metadata_json"] = component_data.pop("metadata")
        
        updated_component = ComponentReadSchema(
            id=1,
            **component_data,
            created_at=datetime.fromisoformat("2024-01-01T00:00:00+00:00"),
            updated_at=datetime.fromisoformat("2024-01-01T01:00:00+00:00")
        )
        mock_component_service.update_component.return_value = updated_component

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service):
            
            response = authenticated_client.put("/api/v1/components/1", json=update_data)
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["name"] == update_data["name"]
            assert response_data["unit_price"] == update_data["unit_price"]
            mock_component_service.update_component.assert_called_once()

    def test_delete_component_success(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test successful component deletion."""
        mock_component_service.delete_component.return_value = True

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service):
            
            response = authenticated_client.delete("/api/v1/components/1")
            
            assert response.status_code == status.HTTP_204_NO_CONTENT
            mock_component_service.delete_component.assert_called_once_with(1, deleted_by_user_id=1)

    def test_delete_component_with_dependencies(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test component deletion with dependency error."""
        mock_component_service.delete_component.side_effect = BusinessLogicError(
            "Component has dependencies and cannot be deleted"
        )

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service):
            
            response = authenticated_client.delete("/api/v1/components/1")
            
            assert response.status_code == status.HTTP_409_CONFLICT
            assert "dependencies" in response.json()["detail"].lower()


class TestComponentSearchEndpoints:
    """Test suite for component search and filtering."""

    def test_list_components_success(
        self, 
        client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        mock_search_result
    ):
        """Test successful component listing."""
        mock_component_service.search_components.return_value = mock_search_result

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service), \
             patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get("/api/v1/components/")
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "items" in response_data
            assert "pagination" in response_data
            mock_component_service.search_components.assert_called_once()

    def test_list_components_with_filters(
        self, 
        client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        mock_search_result
    ):
        """Test component listing with filters."""
        mock_component_service.search_components.return_value = mock_search_result

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service), \
             patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get(
                "/api/v1/components/",
                params={
                    "search_term": "circuit breaker",
                    "category": ComponentCategoryType.PROTECTION_DEVICES.value,
                    "manufacturer": "ABB",
                    "is_preferred": True,
                    "page": 1,
                    "size": 10
                }
            )
            
            assert response.status_code == status.HTTP_200_OK
            mock_component_service.search_components.assert_called_once()
            
            # Verify search parameters were passed correctly
            call_args = mock_component_service.search_components.call_args
            search_params = call_args[0][0]  # First argument
            assert search_params.search_term == "circuit breaker"
            assert search_params.category == ComponentCategoryType.PROTECTION_DEVICES
            assert search_params.manufacturer == "ABB"
            assert search_params.is_preferred is True

    def test_advanced_search_components(
        self, 
        client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        mock_search_result
    ):
        """Test advanced component search."""
        search_data = {
            "search_term": "circuit breaker",
            "category": ComponentCategoryType.PROTECTION_DEVICES.value,
            "component_type": ComponentType.CIRCUIT_BREAKER.value,
            "manufacturer": "ABB",
            "is_preferred": False,
            "is_active": True,
            "min_price": 10.0,
            "max_price": 50.0,
            "currency": "EUR",
            "stock_status": "available",
            "specifications": {
                "electrical": {
                    "current_rating": "16A"
                }
            }
        }
        
        mock_component_service.search_components.return_value = mock_search_result

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service), \
             patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.post("/api/v1/components/search", json=search_data)
            
            assert response.status_code == status.HTTP_200_OK
            mock_component_service.search_components.assert_called_once()


class TestComponentCategoryEndpoints:
    """Test suite for component category and type endpoints."""

    def test_list_component_categories(
        self, 
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test listing component categories."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get("/api/v1/components/categories")
            
            assert response.status_code == status.HTTP_200_OK
            categories = response.json()
            assert isinstance(categories, list)
            assert len(categories) > 0
            
            # Verify category structure
            for category in categories:
                assert "value" in category
                assert "description" in category

    def test_list_component_types(
        self, 
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test listing component types."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get("/api/v1/components/types")
            
            assert response.status_code == status.HTTP_200_OK
            types = response.json()
            assert isinstance(types, list)
            assert len(types) > 0
            
            # Verify type structure
            for comp_type in types:
                assert "value" in comp_type
                assert "description" in comp_type

    def test_list_component_types_filtered_by_category(
        self, 
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test listing component types filtered by category."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get(
                "/api/v1/components/types",
                params={"category": ComponentCategoryType.PROTECTION_DEVICES.value}
            )
            
            assert response.status_code == status.HTTP_200_OK
            types = response.json()
            assert isinstance(types, list)
            # Should have fewer types when filtered by category
            assert len(types) >= 0

    def test_get_components_by_category(
        self, 
        client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test getting components by category."""
        mock_component_service.get_components_by_category.return_value = []

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service), \
             patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get(f"/api/v1/components/by-category/{ComponentCategoryType.PROTECTION_DEVICES.value}")
            
            assert response.status_code == status.HTTP_200_OK
            components = response.json()
            assert isinstance(components, list)
            mock_component_service.get_components_by_category.assert_called_once()

    def test_get_components_by_type(
        self, 
        client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test getting components by type."""
        mock_component_service.get_components_by_type.return_value = []

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service), \
             patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get(f"/api/v1/components/by-type/{ComponentType.CIRCUIT_BREAKER.value}")
            
            assert response.status_code == status.HTTP_200_OK
            components = response.json()
            assert isinstance(components, list)
            mock_component_service.get_components_by_type.assert_called_once()

    def test_get_preferred_components(
        self, 
        client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test getting preferred components."""
        mock_component_service.get_preferred_components.return_value = []

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service), \
             patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get("/api/v1/components/preferred")
            
            assert response.status_code == status.HTTP_200_OK
            components = response.json()
            assert isinstance(components, list)
            mock_component_service.get_preferred_components.assert_called_once()


class TestComponentStatsEndpoint:
    """Test suite for component statistics endpoint."""

    def test_get_component_stats(
        self, 
        client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test getting component statistics."""
        from src.core.schemas.general.component_schemas import ComponentStatsSchema
        
        mock_stats = ComponentStatsSchema(
            total_components=100,
            active_components=95,
            preferred_components=25,
            components_by_category={"protection_devices": 30, "cables": 40},
            components_by_manufacturer={"ABB": 25, "Schneider": 20},
            average_price=Decimal("25.50"),
            price_range={"min": Decimal("5.00"), "max": Decimal("100.00")}
        )
        mock_component_service.get_component_stats.return_value = mock_stats

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service), \
             patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get("/api/v1/components/stats")
            
            assert response.status_code == status.HTTP_200_OK
            stats = response.json()
            assert stats["total_components"] == 100
            assert stats["active_components"] == 95
            assert stats["preferred_components"] == 25
            assert "components_by_category" in stats
            assert "components_by_manufacturer" in stats
            mock_component_service.get_component_stats.assert_called_once()


class TestComponentAuthenticationAndAuthorization:
    """Test suite for authentication and authorization."""

    def test_create_component_without_auth(self, client: TestClient):
        """Test component creation without authentication."""
        component_data = {
            "name": "Test Component",
            "manufacturer": "Test Manufacturer",
            "model_number": "TEST-001",
            "component_type": ComponentType.CIRCUIT_BREAKER.value
        }
        
        response = client.post("/api/v1/components/", json=component_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_get_component_without_auth(self, client: TestClient):
        """Test component retrieval without authentication."""
        response = client.get("/api/v1/components/1")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_list_components_without_auth(self, client: TestClient):
        """Test component listing without authentication."""
        response = client.get("/api/v1/components/")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestComponentErrorHandling:
    """Test suite for error handling scenarios."""

    def test_invalid_component_id_format(
        self, 
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test invalid component ID format."""
        with patch("src.api.v1.component_routes.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get("/api/v1/components/invalid_id")
            
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_invalid_pagination_parameters(
        self, 
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test invalid pagination parameters."""
        with patch("src.api.v1.component_routes.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get("/api/v1/components/", params={"page": 0, "size": -1})
            
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_invalid_enum_values(
        self, 
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test invalid enum values in filters."""
        with patch("src.api.v1.component_routes.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get(
                "/api/v1/components/",
                params={"category": "INVALID_CATEGORY", "component_type": "INVALID_TYPE"}
            )
            
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


# Performance and load testing scenarios
class TestComponentPerformance:
    """Test suite for performance scenarios."""

    def test_large_component_list_pagination(
        self, 
        client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test pagination with large component lists."""
        from src.core.schemas.general.component_schemas import ComponentPaginatedResponseSchema
        from src.core.schemas.base import PaginationSchema
        
        # Mock large result set
        mock_result = ComponentPaginatedResponseSchema(
            items=[],
            pagination=PaginationSchema(
                page=1,
                size=100,
                total=10000,
                pages=100
            )
        )
        mock_component_service.search_components.return_value = mock_result

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service), \
             patch("src.api.v1.component_routes.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.get("/api/v1/components/", params={"size": 100})
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["pagination"]["total"] == 10000
            assert response_data["pagination"]["pages"] == 100

    def test_complex_search_query_performance(
        self, 
        client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test performance with complex search queries."""
        from src.core.schemas.general.component_schemas import ComponentPaginatedResponseSchema
        from src.core.schemas.base import PaginationSchema
        
        mock_result = ComponentPaginatedResponseSchema(
            items=[],
            pagination=PaginationSchema(page=1, size=20, total=0, pages=0)
        )
        mock_component_service.search_components.return_value = mock_result

        complex_search = {
            "search_term": "high voltage circuit breaker protection device",
            "category": ComponentCategoryType.PROTECTION_DEVICES.value,
            "component_type": ComponentType.CIRCUIT_BREAKER.value,
            "manufacturer": "ABB",
            "min_price": 100.0,
            "max_price": 1000.0,
            "specifications": {
                "electrical": {
                    "voltage_rating": "400V",
                    "current_rating": "63A",
                    "breaking_capacity": "10kA"
                },
                "standards_compliance": ["IEC-60898-1"]
            }
        }

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service), \
             patch("src.api.v1.component_routes.require_authenticated_user", return_value=mock_auth_user):
            
            response = client.post("/api/v1/components/search", json=complex_search)
            
            assert response.status_code == status.HTTP_200_OK
            mock_component_service.search_components.assert_called_once()

class TestAdvancedSearchEndpoints:
    """Test cases for advanced search API endpoints."""
    

    def test_advanced_search_endpoint_success(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any],
        sample_advanced_search_request: Dict[str, Any],
        sample_search_response: ComponentAdvancedSearchResponseSchema
    ):
        """Test successful advanced search endpoint."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.search_components_with_builder.return_value = sample_search_response
                mock_service.return_value = mock_service_instance
                
                response = client.post(
                    "/api/v1/components/search/advanced",
                    json=sample_advanced_search_request,
                    params={"page": 1, "size": 20}
                )
                
                assert response.status_code == status.HTTP_200_OK
                
                response_data = response.json()
                assert "items" in response_data
                assert "pagination" in response_data
                assert "search_metadata" in response_data
                assert len(response_data["items"]) == 1
                assert response_data["items"][0]["component"]["manufacturer"] == "Schneider Electric"
                assert response_data["search_metadata"]["search_type"] == "advanced_builder"
    
    def test_advanced_search_endpoint_validation_error(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test advanced search endpoint with validation error."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            # Invalid request data (missing required fields)
            invalid_request = {
                "basic_filters": [
                    {
                        "field": "manufacturer",
                        "operator": "invalid_operator",  # Invalid operator
                        "value": "Schneider Electric"
                    }
                ]
            }
            
            response = client.post(
                "/api/v1/components/search/advanced",
                json=invalid_request
            )
            
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_relevance_search_endpoint_success(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any],
        sample_search_response: ComponentAdvancedSearchResponseSchema
    ):
        """Test successful relevance search endpoint."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.search_components_with_relevance.return_value = sample_search_response
                mock_service.return_value = mock_service_instance
                
                response = client.get(
                    "/api/v1/components/search/relevance",
                    params={
                        "search_term": "circuit breaker",
                        "search_fields": "name,description",
                        "fuzzy": False,
                        "page": 1,
                        "size": 20
                    }
                )
                
                assert response.status_code == status.HTTP_200_OK
                
                response_data = response.json()
                assert "items" in response_data
                assert len(response_data["items"]) == 1
                assert response_data["items"][0]["relevance_score"] == 0.95
    
    def test_relevance_search_endpoint_invalid_fields(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test relevance search endpoint with invalid search fields."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            response = client.get(
                "/api/v1/components/search/relevance",
                params={
                    "search_term": "circuit breaker",
                    "search_fields": "invalid_field,another_invalid",
                    "page": 1,
                    "size": 20
                }
            )
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            response_data = response.json()
            assert "Invalid search fields" in response_data["detail"]


class TestEnhancedBulkOperationsEndpoints:
    """Test cases for enhanced bulk operations API endpoints."""


    def test_enhanced_bulk_create_endpoint_success(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any],
        sample_bulk_create_request: List[Dict[str, Any]]
    ):
        """Test successful enhanced bulk create endpoint."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.bulk_create_with_validation.return_value = {
                    "total_processed": 2,
                    "created": 2,
                    "errors": 0,
                    "success_rate": 1.0,
                    "created_components": [],
                    "validation_errors": []
                }
                mock_service.return_value = mock_service_instance
                
                response = client.post(
                    "/api/v1/components/bulk/create-validated",
                    json=sample_bulk_create_request,
                    params={
                        "validate_duplicates": True,
                        "batch_size": 100
                    }
                )
                
                assert response.status_code == status.HTTP_201_CREATED
                
                response_data = response.json()
                assert response_data["total_processed"] == 2
                assert response_data["created"] == 2
                assert response_data["errors"] == 0
                assert response_data["success_rate"] == 1.0
    
    def test_selective_bulk_update_endpoint_success(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test successful selective bulk update endpoint."""
        update_data = [
            {
                "id": 1,
                "unit_price": 28.99,
                "is_preferred": True
            },
            {
                "id": 2,
                "description": "Updated description"
            }
        ]
        
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.bulk_update_selective.return_value = {
                    "total_processed": 2,
                    "updated": 2,
                    "errors": 0,
                    "success_rate": 1.0,
                    "validation_errors": []
                }
                mock_service.return_value = mock_service_instance
                
                response = client.put(
                    "/api/v1/components/bulk/update-selective",
                    json=update_data,
                    params={"batch_size": 100}
                )
                
                assert response.status_code == status.HTTP_200_OK
                
                response_data = response.json()
                assert response_data["total_processed"] == 2
                assert response_data["updated"] == 2
                assert response_data["success_rate"] == 1.0
    
    def test_bulk_delete_endpoint_success(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test successful bulk delete endpoint."""
        component_ids = [1, 2, 3, 4, 5]
        
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.bulk_delete_components.return_value = {
                    "total_processed": 5,
                    "deleted": 5,
                    "not_found": 0,
                    "success_rate": 1.0,
                    "not_found_ids": [],
                    "delete_type": "soft"
                }
                mock_service.return_value = mock_service_instance
                
                response = client.delete(
                    "/api/v1/components/bulk/delete",
                    json=component_ids,
                    params={"soft_delete": True}
                )
                
                assert response.status_code == status.HTTP_200_OK
                
                response_data = response.json()
                assert response_data["total_processed"] == 5
                assert response_data["deleted"] == 5
                assert response_data["delete_type"] == "soft"


class TestPerformanceOptimizationEndpoints:
    """Test cases for performance optimization API endpoints."""

    def test_get_performance_metrics_endpoint(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test get performance metrics endpoint."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.get_performance_metrics.return_value = {
                    "component_statistics": {
                        "total_components": 1000,
                        "active_components": 950,
                        "preferred_components": 100
                    },
                    "cache_performance": {
                        "memory_cache_size": 50,
                        "redis_connected": True
                    },
                    "query_performance": {
                        "slow_queries_count": 2,
                        "slowest_query_time": 1.5
                    },
                    "system_health": {
                        "database_connected": True,
                        "cache_connected": True,
                        "performance_monitoring_active": True
                    }
                }
                mock_service.return_value = mock_service_instance
                
                response = client.get("/api/v1/components/performance/metrics")
                
                assert response.status_code == status.HTTP_200_OK
                
                response_data = response.json()
                assert "component_statistics" in response_data
                assert "cache_performance" in response_data
                assert "query_performance" in response_data
                assert "system_health" in response_data
                assert response_data["component_statistics"]["total_components"] == 1000
    
    def test_optimize_system_performance_endpoint(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test optimize system performance endpoint."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.optimize_system_performance.return_value = {
                    "cache_warming": True,
                    "cache_cleanup": True,
                    "index_analysis": True,
                    "query_optimization": True,
                    "errors": [],
                    "index_suggestions": [
                        "CREATE INDEX CONCURRENTLY idx_components_manufacturer_model ON components(manufacturer, model_number);"
                    ]
                }
                mock_service.return_value = mock_service_instance
                
                response = client.post("/api/v1/components/performance/optimize")
                
                assert response.status_code == status.HTTP_200_OK
                
                response_data = response.json()
                assert response_data["cache_warming"] == True
                assert response_data["cache_cleanup"] == True
                assert response_data["index_analysis"] == True
                assert len(response_data["errors"]) == 0
    
    def test_invalidate_cache_endpoint(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test invalidate cache endpoint."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.invalidate_component_cache.return_value = {
                    "success": True,
                    "component_id": 123,
                    "scope": "specific",
                    "timestamp": "2024-01-01T00:00:00Z"
                }
                mock_service.return_value = mock_service_instance
                
                response = client.delete(
                    "/api/v1/components/cache/invalidate",
                    params={"component_id": 123}
                )
                
                assert response.status_code == status.HTTP_200_OK
                
                response_data = response.json()
                assert response_data["success"] == True
                assert response_data["component_id"] == 123
                assert response_data["scope"] == "specific"
